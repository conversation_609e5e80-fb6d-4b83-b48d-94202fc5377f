let lastMousePosition = { x: 0, y: 0 };

// Track mouse position
document.addEventListener('mousemove', (event) => {
  lastMousePosition.x = event.clientX;
  lastMousePosition.y = event.clientY;
});

// Listen for Ctrl+Shift+Q and Ctrl+Shift+E
document.addEventListener('keydown', (event) => {
  if (event.ctrlKey && event.shiftKey && event.key === 'Q') {
    event.preventDefault();
    copyTextUnderMouse();
  }

  if (event.ctrlKey && event.shiftKey && event.key === 'E') {
    event.preventDefault();
    copySelectedText();
  }
});

function copyTextUnderMouse() {
  console.log('Copy function triggered');
  console.log('Mouse position:', lastMousePosition);

  element = document.elementFromPoint(
    lastMousePosition.x,
    lastMousePosition.y
  );

  console.log('Element found:', element);

  if (element) {
    // Get text content
    element = element.parentElement
    const text = element.textContent.trim();
    console.log("Trigger:" + lastMousePosition.x + "," + lastMousePosition.y + " " + text);

    // Copy to clipboard
    navigator.clipboard.writeText(text)
      .then(() => {
        // Show feedback
        showFeedback('Copied to clipboard!');
      })
      .catch(err => {
        console.error('Failed to copy: ', err);
        showFeedback('Failed to copy text');
      });
  }
}

function copySelectedText() {
  console.log('Copy selected text function triggered');

  // Get the selected text
  const selection = window.getSelection();
  const selectedText = selection.toString().trim();

  console.log('Selected text:', selectedText);

  if (selectedText) {
    // Copy to clipboard
    navigator.clipboard.writeText(selectedText)
      .then(() => {
        // Show feedback
        showFeedback('Selected text copied to clipboard!');
      })
      .catch(err => {
        console.error('Failed to copy selected text: ', err);
        showFeedback('Failed to copy selected text');
      });
  } else {
    showFeedback('No text selected');
  }
}

function showFeedback(message) {
  // Create feedback element
  const feedback = document.createElement('div');
  feedback.textContent = message;
  feedback.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #333;
    color: white;
    padding: 10px 15px;
    border-radius: 4px;
    z-index: 10000;
    font-family: Arial, sans-serif;
  `;

  document.body.appendChild(feedback);

  // Remove after 2 seconds
  setTimeout(() => {
    document.body.removeChild(feedback);
  }, 2000);
}

// Add this to test clipboard access
function testClipboardAccess() {
  navigator.clipboard.writeText('Test clipboard access')
    .then(() => debugLog('Clipboard access successful'))
    .catch(err => debugLog('Clipboard access failed:', err));
}

// Call this function when extension loads
document.addEventListener('DOMContentLoaded', testClipboardAccess);


